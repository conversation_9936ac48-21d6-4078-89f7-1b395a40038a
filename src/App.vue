<script setup lang="ts">
import lottie, { AnimationItem } from 'lottie-web'
import useRouteCache from '@/stores/modules/routeCache'
import useAgentCreateStore from './stores/modules/agentCreate'
import useAppStore from './stores/modules/app'
import useUserStore from './stores/modules/user'
import useLoadingStore from '@/stores/modules/loading'
import useNotifyStore from '@/stores/modules/notification.ts'
import NoticeAndPoster from '@/components/NoticeAndPoster/index.vue'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { navigatorLanguage } from '@/utils/lang'
import { getDeviceFingerprint } from '@/utils/tools'
import useWs from '@/hooks/useWs.ts'
import Loading from '@/assets/lottie/loading.json'
import VersionUpdate from '@/components/GlobalPopup/VersionUpdate.vue'
import useVersionStore from '@/stores/modules/version.ts'
import { locale } from '@/utils/i18n.ts'

const route = useRoute()
const loadingStore = useLoadingStore()
const agentCreateStore = useAgentCreateStore()
const appStore = useAppStore()
const userStore = useUserStore()
const versionStore = useVersionStore()
const notifyStore = useNotifyStore()
const loadingRef = ref(null)
const loadingInstance = ref<AnimationItem | null>(null)

const keepAliveRouteNames = computed(() => {
  return useRouteCache().routeCaches as string[]
})

function openPreference() {
  appStore.showPreference = true
}

const enterApp = () => {
  if (window.location.search.includes('isLivco=1')) {
    console.log('isUg start App', new Date().getTime())
    appStore.isUg = true
  }
  let firstTimeOpening = sessionStorage.getItem('enterApp') || null
  if (!firstTimeOpening) {
    sessionStorage.setItem('enterApp', 'enter')
    console.log('eventReport', new Date().getTime())
    eventReport({ event_type: EventTypeEnum.ENTER_APP, front_address: route?.name as string }).catch(() => {})
  }
  locale.value = navigatorLanguage()
  appStore.language = locale.value
  appStore.languageText = localStorage.getItem('languageText')
}

enterApp()

document.addEventListener('visibilitychange', () => {
  if (document.visibilityState !== 'visible') {
    Howler.stop()
  }
})

watch(
  () => loadingStore.isLoading,
  (val) => {
    if (val) {
      nextTick(() => {
        loadingInstance.value = lottie.loadAnimation({
          container: loadingRef.value, // 容器
          renderer: 'svg', // 通过svg或canvas渲染
          loop: true, // 是否循环
          autoplay: true, // 是否自动播放
          animationData: Loading // 动画文件
        })
      })
    } else {
      loadingInstance.value?.destroy()
    }
  }
)

onMounted(() => {
  versionStore.getLatestVersionHandle('home')
  console.log('and', window.JsAndroid)
  if (typeof window !== 'undefined' && (window as any).JsAndroid) {
    console.log('android')
    appStore.isAndroid = true
  }
  getDeviceFingerprint()
  if (userStore.isLogin) {
    console.log(new Date().getTime(), 'onMounted start')
    userStore.getUserInfo()
    useWs.connect()
  }
})
onUnmounted(() => {
  notifyStore.clearMqttInstance()
})
</script>

<template>
  <VanConfigProvider
    theme="dark"
    class="w-full h-full"
  >
    <NavBar />
    <div
      class="w-full"
      :class="(route.meta.level as number) > 1 && route.meta.title ? 'had-bar-height' : 'h-full'"
    >
      <div
        v-if="loadingStore.isLoading"
        id="initial-loading"
      >
        <div
          class="loading-lottie"
          ref="loadingRef"
        ></div>
      </div>
      <router-view v-slot="{ Component, route }">
        <!--        <transition :name="routeTransitionName">-->
        <keep-alive :include="keepAliveRouteNames">
          <component
            :is="Component"
            :key="route.name"
          />
        </keep-alive>
        <!--        </transition>-->
      </router-view>
    </div>
    <TabBar />
    <CreateAgentSelectPopup :show="agentCreateStore.showAgentCreateModePopup" />
    <Login @go-preference="openPreference" />
    <PreferencePopup />
    <CancelLogoffPopup />
    <LangPopup v-if="appStore.showLangPopup" />
    <AgreementPopup v-if="appStore.showAgreement" />
    <MemberGuide v-model="appStore.showMemberGuide" />
    <!-- 消息、海报通知，只在首次登录\每次登录\每天一次展示 -->
    <NoticeAndPoster ref="newNoticeRef" />
    <NewerPopup />
    <Transition name="app-tips">
      <AppTips v-if="appStore.showAppTips" />
    </Transition>
    <VersionUpdate />
  </VanConfigProvider>
</template>
<style lang="scss" scoped>
.had-bar-height {
  height: calc(100% - var(--van-nav-bar-height));
  padding-top: var(--van-nav-bar-height);
}

.loading-lottie {
}
</style>
